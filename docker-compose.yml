services:
  postgres:
    image: postgres:16
    container_name: personality_clone_postgres
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: personality_clone_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  # 暂时注释掉以下服务，聚焦核心功能
  # 当需要时可以逐一开启

  # neo4j:
  #   image: neo4j:5
  #   container_name: personality_clone_neo4j
  #   environment:
  #     NEO4J_AUTH: neo4j/password
  #     NEO4J_PLUGINS: '["apoc"]'
  #   ports:
  #     - "7474:7474"  # Neo4j Browser
  #     - "7687:7687"  # Bolt protocol
  #   volumes:
  #     - neo4j_data:/data
  #     - neo4j_logs:/logs
  #   restart: unless-stopped

  # chromadb:
  #   image: chromadb/chroma:latest
  #   container_name: personality_clone_chroma
  #   ports:
  #     - "8001:8000"
  #   volumes:
  #     - chroma_data:/chroma/chroma
  #   environment:
  #     - CHROMA_SERVER_HOST=0.0.0.0
  #     - CHROMA_SERVER_HTTP_PORT=8000
  #   restart: unless-stopped

  # redis:
  #   image: redis:7-alpine
  #   container_name: personality_clone_redis
  #   ports:
  #     - "6500:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped

  # elasticsearch:
  #   image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
  #   container_name: personality_clone_elasticsearch
  #   environment:
  #     - discovery.type=single-node
  #     - xpack.security.enabled=false
  #     - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
  #   ports:
  #     - "9200:9200"
  #   volumes:
  #     - elasticsearch_data:/usr/share/elasticsearch/data
  #   restart: unless-stopped

volumes:
  postgres_data:
  # 暂时注释掉未使用的数据卷
  # neo4j_data:
  # neo4j_logs:
  # chroma_data:
  # redis_data:
  # elasticsearch_data:
