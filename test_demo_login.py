#!/usr/bin/env python3
"""
测试演示账号登录
"""

import requests
import json

def test_demo_login():
    """测试演示账号登录"""
    print("🔍 测试演示账号登录...")
    
    try:
        # 测试登录
        login_data = {
            "username": "demo",
            "password": "demo123"
        }
        
        response = requests.post(
            "http://localhost:8000/auth/login",
            json=login_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 演示账号登录成功！")
            print(f"   用户ID: {result.get('user_id')}")
            print(f"   Token: {result.get('token')[:20]}...")
            
            # 测试获取人格档案
            token = result.get('token')
            headers = {"Authorization": f"Bearer {token}"}
            
            personalities_response = requests.get(
                "http://localhost:8000/personalities",
                headers=headers,
                timeout=10
            )
            
            if personalities_response.status_code == 200:
                personalities = personalities_response.json()
                print(f"✅ 获取人格档案成功，共 {len(personalities)} 个档案")
                
                for p in personalities:
                    print(f"   - {p.get('target_name')}: {p.get('completion_percentage', 0):.1f}%")
            else:
                print(f"❌ 获取人格档案失败: {personalities_response.status_code}")
                
        else:
            print(f"❌ 演示账号登录失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_demo_login()
