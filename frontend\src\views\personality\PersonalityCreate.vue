<template>
  <div class="personality-create">
    <div class="page-header">
      <h1>创建人格档案</h1>
      <p>通过AI分析创建详细的人格档案</p>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="目标人物姓名" prop="target_name">
          <el-input
            v-model="form.target_name"
            placeholder="请输入要分析的人物姓名"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="人物描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请简要描述这个人的基本信息、性格特点、兴趣爱好等"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >
            <el-icon><Plus /></el-icon>
            创建档案
          </el-button>
          <el-button @click="handleCancel">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { apiMethods } from '../../utils/api'

const router = useRouter()
const formRef = ref()
const loading = ref(false)

// 表单数据
const form = reactive({
  target_name: '',
  description: ''
})

// 验证规则
const rules = {
  target_name: [
    { required: true, message: '请输入目标人物姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入人物描述', trigger: 'blur' },
    { min: 10, max: 500, message: '描述长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const response = await apiMethods.post('/personalities', form)

    ElMessage.success('人格档案创建成功！')
    router.push('/personalities')

  } catch (error) {
    console.error('创建失败:', error)
    ElMessage.error(error.response?.data?.detail || '创建失败，请重试')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  router.push('/personalities')
}
</script>

<style scoped>
.personality-create {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style>
