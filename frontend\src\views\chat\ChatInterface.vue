<template>
  <div class="chat-interface">
    <!-- 人格选择器 -->
    <el-card v-if="!selectedPersonality" class="personality-selector">
      <template #header>
        <h3>选择要对话的人格档案</h3>
      </template>

      <div v-if="personalities.length === 0" class="empty-state">
        <el-icon size="48"><User /></el-icon>
        <p>暂无人格档案</p>
        <el-button type="primary" @click="createPersonality">
          创建人格档案
        </el-button>
      </div>

      <div v-else class="personality-list">
        <div
          v-for="personality in personalities"
          :key="personality.personality_id"
          class="personality-item"
          @click="selectPersonality(personality)"
        >
          <div class="personality-info">
            <h4>{{ personality.target_name }}</h4>
            <p>{{ personality.description || '暂无描述' }}</p>
            <el-progress
              :percentage="Math.round(personality.completion_percentage || 0)"
              :stroke-width="4"
              :show-text="false"
            />
          </div>
          <el-button type="primary">开始对话</el-button>
        </div>
      </div>
    </el-card>

    <!-- 对话界面 -->
    <div v-else class="chat-container">
      <!-- 对话头部 -->
      <el-card class="chat-header">
        <div class="header-content">
          <div class="personality-info">
            <h3>{{ selectedPersonality.target_name }}</h3>
            <p>完成度: {{ Math.round(selectedPersonality.completion_percentage || 0) }}%</p>
          </div>
          <div class="actions">
            <el-button @click="selectedPersonality = null">
              <el-icon><ArrowLeft /></el-icon>
              返回选择
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 消息列表 -->
      <el-card class="chat-messages" v-loading="loading">
        <div class="messages-container" ref="messagesContainer">
          <div
            v-for="message in messages"
            :key="message.id"
            :class="['message', message.sender]"
          >
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>

          <div v-if="messages.length === 0" class="empty-messages">
            <el-icon size="48"><ChatDotRound /></el-icon>
            <p>开始与 {{ selectedPersonality.target_name }} 对话吧！</p>
          </div>
        </div>
      </el-card>

      <!-- 输入区域 -->
      <el-card class="chat-input">
        <div class="input-container">
          <el-input
            v-model="userInput"
            type="textarea"
            :rows="3"
            placeholder="请输入您的消息..."
            @keydown.ctrl.enter="sendMessage"
          />
          <div class="input-actions">
            <span class="tip">Ctrl + Enter 发送</span>
            <el-button
              type="primary"
              :loading="sending"
              :disabled="!userInput.trim()"
              @click="sendMessage"
            >
              发送
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ChatDotRound, User, ArrowLeft } from '@element-plus/icons-vue'
import { apiMethods } from '../../utils/api'

const router = useRouter()
const route = useRoute()

// 响应式数据
const personalities = ref([])
const selectedPersonality = ref(null)
const messages = ref([])
const userInput = ref('')
const loading = ref(false)
const sending = ref(false)
const conversationId = ref(null)
const messagesContainer = ref(null)

// 方法
const createPersonality = () => {
  router.push('/personalities/create')
}

const selectPersonality = async (personality) => {
  selectedPersonality.value = personality
  await startConversation()
}

const startConversation = async () => {
  try {
    loading.value = true

    // 使用新的模拟API启动对话
    const response = await apiMethods.simulation.start(
      selectedPersonality.value.personality_id,
      '你好，很高兴认识你！'
    )

    conversationId.value = response.data.conversation_id

    // 添加用户的初始消息和AI的回复
    messages.value = [
      {
        id: Date.now(),
        sender: 'user',
        content: '你好，很高兴认识你！',
        timestamp: new Date()
      },
      {
        id: Date.now() + 1,
        sender: 'ai',
        content: response.data.ai_response,
        timestamp: new Date()
      }
    ]

    await scrollToBottom()
  } catch (error) {
    console.error('启动对话失败:', error)
    ElMessage.error('启动对话失败，请重试')
  } finally {
    loading.value = false
  }
}

const sendMessage = async () => {
  if (!userInput.value.trim() || sending.value) return

  const messageText = userInput.value.trim()
  userInput.value = ''

  // 添加用户消息
  messages.value.push({
    id: Date.now(),
    sender: 'user',
    content: messageText,
    timestamp: new Date()
  })

  await scrollToBottom()

  try {
    sending.value = true

    // 使用新的模拟API发送消息
    const response = await apiMethods.simulation.chat(
      conversationId.value,
      messageText
    )

    // 添加AI回复
    messages.value.push({
      id: Date.now() + 1,
      sender: 'ai',
      content: response.data.ai_response,
      timestamp: new Date()
    })

    await scrollToBottom()
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送失败，请重试')
  } finally {
    sending.value = false
  }
}

const formatTime = (time) => {
  return new Date(time).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const loadPersonalities = async () => {
  try {
    const response = await apiMethods.personalities.list()
    personalities.value = response.data || []

    // 如果URL中有personalityId参数，自动选择
    const personalityId = route.params.personalityId
    if (personalityId) {
      const personality = personalities.value.find(p => p.personality_id === personalityId)
      if (personality) {
        await selectPersonality(personality)
      }
    }
  } catch (error) {
    console.error('加载人格档案失败:', error)
    ElMessage.error('加载失败，请重试')
  }
}

// 生命周期
onMounted(() => {
  loadPersonalities()
})
</script>

<style scoped>
.chat-interface {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.personality-selector {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.personality-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.personality-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.personality-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.personality-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.personality-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chat-header {
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-messages {
  flex: 1;
  overflow: hidden;
}

.messages-container {
  height: 400px;
  overflow-y: auto;
  padding: 10px 0;
}

.message {
  margin-bottom: 20px;
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.ai {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
}

.message.user .message-content {
  background: #409eff;
  color: white;
}

.message.ai .message-content {
  background: #f5f7fa;
  color: #303133;
}

.message-text {
  line-height: 1.5;
  word-wrap: break-word;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 5px;
}

.empty-messages {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.chat-input {
  flex-shrink: 0;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tip {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-interface {
    padding: 10px;
  }

  .message-content {
    max-width: 85%;
  }

  .header-content {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
