<template>
  <div class="personality-list">
    <div class="page-header">
      <h1>人格档案</h1>
      <el-button type="primary" @click="createPersonality">
        <el-icon><Plus /></el-icon>
        创建新档案
      </el-button>
    </div>

    <el-card v-loading="loading">
      <div v-if="personalities.length === 0" class="empty-state">
        <el-icon size="64"><User /></el-icon>
        <h3>暂无人格档案</h3>
        <p>点击上方按钮创建您的第一个人格档案</p>
        <el-button type="primary" @click="createPersonality">
          <el-icon><Plus /></el-icon>
          立即创建
        </el-button>
      </div>

      <div v-else class="personality-grid">
        <div
          v-for="personality in personalities"
          :key="personality.profile_id"
          class="personality-card"
          @click="viewPersonality(personality.profile_id)"
        >
          <div class="card-header">
            <h3>{{ personality.target_name }}</h3>
            <el-tag :type="getStatusType(personality.completion_percentage)">
              {{ getStatusText(personality.completion_percentage) }}
            </el-tag>
          </div>

          <div class="card-content">
            <p class="description">{{ personality.description || '暂无描述' }}</p>

            <div class="progress-section">
              <div class="progress-label">
                <span>完成度</span>
                <span>{{ Math.round(personality.completion_percentage || 0) }}%</span>
              </div>
              <el-progress
                :percentage="Math.round(personality.completion_percentage || 0)"
                :stroke-width="6"
                :show-text="false"
              />
            </div>
          </div>

          <div class="card-footer">
            <span class="create-time">
              创建于 {{ formatDate(personality.created_at) }}
            </span>
            <div class="actions">
              <el-button
                size="small"
                type="primary"
                @click.stop="startChat(personality.profile_id)"
              >
                开始对话
              </el-button>
              <el-button
                size="small"
                @click.stop="viewPersonality(personality.profile_id)"
              >
                查看详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, User } from '@element-plus/icons-vue'
import { apiMethods } from '../../utils/api'

const router = useRouter()
const loading = ref(false)
const personalities = ref([])

// 方法
const createPersonality = () => {
  router.push('/personalities/create')
}

const viewPersonality = (id) => {
  router.push(`/personalities/${id}`)
}

const startChat = (personalityId) => {
  router.push(`/chat/${personalityId}`)
}

const getStatusType = (percentage) => {
  if (percentage >= 80) return 'success'
  if (percentage >= 50) return 'warning'
  return 'info'
}

const getStatusText = (percentage) => {
  if (percentage >= 80) return '已完成'
  if (percentage >= 50) return '进行中'
  return '刚开始'
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const loadPersonalities = async () => {
  try {
    loading.value = true
    const response = await apiMethods.get('/personalities')
    personalities.value = response.data || []
  } catch (error) {
    console.error('加载人格档案失败:', error)
    ElMessage.error('加载失败，请重试')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadPersonalities()
})
</script>

<style scoped>
.personality-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state h3 {
  margin: 20px 0 10px;
  color: #606266;
}

.personality-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.personality-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.personality-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.card-content {
  margin-bottom: 15px;
}

.description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.progress-section {
  margin-bottom: 10px;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.actions {
  display: flex;
  gap: 8px;
}
</style>
