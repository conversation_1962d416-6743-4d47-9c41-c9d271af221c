#!/usr/bin/env python3
"""
创建演示用户和数据的脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from app.database.db_session import get_db_session, init_database
from app.database.models import User, PersonalityProfile, Entity, Belief, Event
import uuid

async def create_demo_user():
    """创建演示用户和数据"""
    print("🔄 正在创建演示用户...")
    
    try:
        # 初始化数据库
        await init_database()
        
        # 获取数据库会话
        async for db in get_db_session():
            # 检查演示用户是否已存在
            result = await db.execute(select(User).where(User.username == "demo"))
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                print("✅ 演示用户已存在")
                print(f"   用户ID: {existing_user.user_id}")
                print(f"   用户名: {existing_user.username}")
                print(f"   邮箱: {existing_user.email}")
                return existing_user
            
            # 创建演示用户
            demo_user = User(
                username="demo",
                email="<EMAIL>",
                hashed_password="demo123",  # 简化版，实际应该加密
                is_active=True
            )
            
            db.add(demo_user)
            await db.commit()
            await db.refresh(demo_user)
            
            print("✅ 演示用户创建成功")
            print(f"   用户ID: {demo_user.user_id}")
            print(f"   用户名: {demo_user.username}")
            print(f"   邮箱: {demo_user.email}")
            print(f"   密码: demo123")
            
            # 创建演示人格档案
            demo_personality = PersonalityProfile(
                user_id=demo_user.user_id,
                target_name="演示人格",
                description="这是一个演示用的人格档案，展示系统的各项功能",
                completion_percentage=75.0,
                openness_score=0.7,
                conscientiousness_score=0.6,
                extraversion_score=0.8,
                agreeableness_score=0.9,
                neuroticism_score=0.3,
                decision_making_speed=0.6,
                risk_tolerance=0.4,
                average_response_length=150.0,
                vocabulary_complexity=0.7,
                emotional_expressiveness=0.8
            )
            
            db.add(demo_personality)
            await db.commit()
            await db.refresh(demo_personality)
            
            print("✅ 演示人格档案创建成功")
            print(f"   档案ID: {demo_personality.profile_id}")
            print(f"   目标人物: {demo_personality.target_name}")
            print(f"   完成度: {demo_personality.completion_percentage}%")
            
            # 创建一些演示实体
            demo_entities = [
                Entity(
                    personality_id=demo_personality.profile_id,
                    name="家人",
                    entity_type="person",
                    relationship_type="family",
                    emotional_valence=0.9,
                    importance_score=0.95,
                    profile={"description": "最重要的人际关系"}
                ),
                Entity(
                    personality_id=demo_personality.profile_id,
                    name="朋友",
                    entity_type="person", 
                    relationship_type="friend",
                    emotional_valence=0.8,
                    importance_score=0.8,
                    profile={"description": "社交圈中的重要关系"}
                ),
                Entity(
                    personality_id=demo_personality.profile_id,
                    name="工作",
                    entity_type="concept",
                    relationship_type="professional",
                    emotional_valence=0.6,
                    importance_score=0.7,
                    profile={"description": "职业发展相关"}
                )
            ]
            
            for entity in demo_entities:
                db.add(entity)
            
            # 创建一些演示信念
            demo_beliefs = [
                Belief(
                    personality_id=demo_personality.profile_id,
                    statement="诚实是最重要的品质",
                    belief_category="moral",
                    conviction_strength=0.9,
                    flexibility_score=0.2,
                    origin_context="从小的家庭教育",
                    full_explanation="认为诚实是人际关系的基础"
                ),
                Belief(
                    personality_id=demo_personality.profile_id,
                    statement="努力工作会有回报",
                    belief_category="personal",
                    conviction_strength=0.8,
                    flexibility_score=0.4,
                    origin_context="个人经历总结",
                    full_explanation="相信通过努力可以实现目标"
                ),
                Belief(
                    personality_id=demo_personality.profile_id,
                    statement="家庭比事业更重要",
                    belief_category="personal",
                    conviction_strength=0.7,
                    flexibility_score=0.6,
                    origin_context="生活价值观",
                    full_explanation="认为家庭幸福是人生的根本"
                )
            ]
            
            for belief in demo_beliefs:
                db.add(belief)
            
            # 创建一些演示事件
            demo_events = [
                Event(
                    personality_id=demo_personality.profile_id,
                    title="大学毕业",
                    age_at_event=22,
                    life_stage="young_adult",
                    event_type="achievement",
                    emotional_impact=0.8,
                    centrality_score=0.9,
                    memory_vividness=0.9,
                    lessons_learned=["坚持的重要性", "知识的价值"],
                    full_narrative="经过四年的努力学习，终于顺利毕业了"
                ),
                Event(
                    personality_id=demo_personality.profile_id,
                    title="第一份工作",
                    age_at_event=23,
                    life_stage="young_adult",
                    event_type="career",
                    emotional_impact=0.7,
                    centrality_score=0.8,
                    memory_vividness=0.8,
                    lessons_learned=["适应能力", "团队合作"],
                    full_narrative="进入职场，开始了人生新的阶段"
                )
            ]
            
            for event in demo_events:
                db.add(event)
            
            await db.commit()
            
            print("✅ 演示数据创建完成")
            print(f"   - 创建了 {len(demo_entities)} 个实体")
            print(f"   - 创建了 {len(demo_beliefs)} 个信念")
            print(f"   - 创建了 {len(demo_events)} 个事件")
            
            return demo_user
            
    except Exception as e:
        print(f"❌ 创建演示用户失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """主函数"""
    print("🚀 开始创建演示数据...")
    
    user = await create_demo_user()
    
    if user:
        print("\n🎉 演示数据创建成功！")
        print("\n📝 登录信息:")
        print("   用户名: demo")
        print("   密码: demo123")
        print("\n🌐 现在您可以使用演示账号登录系统了！")
    else:
        print("\n❌ 演示数据创建失败")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
