"""
Enhanced FastAPI application for 100% personality cloning system
"""

import os
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel
import structlog

from app.database.db_session import get_db_session, init_database, cleanup_database
from app.database.models import User, PersonalityProfile, Conversation, Message
from app.services.enhanced_socratic_cycle import EnhancedSocratic<PERSON>ycle
from app.llm.schemas import (
    DeepAnalysisResult, QuestioningStrategy, EmpatheticQuestion,
    SituationalResponse, PersonalitySimilarity
)
# 导入模拟API
from app.api.endpoints import simulation

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Application lifecycle management
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting personality cloning system")
    await init_database()
    yield
    # Shutdown
    logger.info("Shutting down personality cloning system")
    await cleanup_database()

# Create FastAPI app
app = FastAPI(
    title="100% Personality Cloning System",
    description="Advanced AI system for complete personality replication",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Initialize services
socratic_cycle = EnhancedSocraticCycle()

# Include API routers
app.include_router(simulation.router, prefix="/api/v1", tags=["Simulation"])

# === Pydantic Models ===

class UserCreate(BaseModel):
    username: str
    email: str
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class PersonalityCreate(BaseModel):
    target_name: str
    description: Optional[str] = None

class ChatRequest(BaseModel):
    user_input: str
    conversation_id: Optional[str] = None

class PredictionRequest(BaseModel):
    situation: str
    personality_id: str

class ValidationRequest(BaseModel):
    personality_id: str
    reference_data: Optional[Dict[str, Any]] = None

# === Authentication ===

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db_session)
) -> User:
    """Get current authenticated user"""
    # Simplified authentication - in production, use proper JWT validation
    token = credentials.credentials
    
    # For demo purposes, we'll use a simple token format: "user_id"
    try:
        user_id = token
        result = await db.execute(select(User).where(User.user_id == user_id))
        user = result.scalar_one_or_none()
        
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )
        
        return user
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )

# === API Endpoints ===

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "100% Personality Cloning System is running",
        "version": "1.0.0",
        "status": "healthy"
    }

@app.post("/auth/register")
async def register_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db_session)
):
    """Register a new user"""
    try:
        # Check if user already exists
        existing_user = await db.execute(
            select(User).where(User.username == user_data.username)
        )
        if existing_user.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists"
            )
        
        # Create new user (simplified - in production, hash the password)
        new_user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=user_data.password  # Should be hashed!
        )
        
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        logger.info("User registered", user_id=str(new_user.user_id))
        
        return {
            "message": "User registered successfully",
            "user_id": str(new_user.user_id),
            "token": str(new_user.user_id)  # Simplified token
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("User registration failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@app.post("/auth/login")
async def login_user(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_db_session)
):
    """Login user"""
    try:
        # Find user
        result = await db.execute(
            select(User).where(User.username == login_data.username)
        )
        user = result.scalar_one_or_none()
        
        if not user or user.hashed_password != login_data.password:  # Simplified check
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        logger.info("User logged in", user_id=str(user.user_id))
        
        return {
            "message": "Login successful",
            "user_id": str(user.user_id),
            "token": str(user.user_id)  # Simplified token
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("User login failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@app.post("/personalities")
async def create_personality(
    personality_data: PersonalityCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Create a new personality profile"""
    try:
        new_personality = PersonalityProfile(
            user_id=current_user.user_id,
            target_name=personality_data.target_name,
            description=personality_data.description
        )
        
        db.add(new_personality)
        await db.commit()
        await db.refresh(new_personality)
        
        logger.info(
            "Personality profile created",
            personality_id=str(new_personality.profile_id),
            target_name=personality_data.target_name
        )
        
        return {
            "message": "Personality profile created",
            "personality_id": str(new_personality.profile_id),
            "target_name": personality_data.target_name,
            "completion_percentage": 0.0
        }
        
    except Exception as e:
        logger.error("Personality creation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create personality profile"
        )

@app.get("/personalities")
async def list_personalities(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """List user's personality profiles"""
    try:
        result = await db.execute(
            select(PersonalityProfile).where(PersonalityProfile.user_id == current_user.user_id)
        )
        personalities = result.scalars().all()
        
        return [
            {
                "personality_id": str(p.profile_id),
                "target_name": p.target_name,
                "description": p.description,
                "completion_percentage": p.completion_percentage,
                "created_at": p.created_at.isoformat() if p.created_at else None
            }
            for p in personalities
        ]
        
    except Exception as e:
        logger.error("Failed to list personalities", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve personality profiles"
        )

@app.post("/chat/start/{personality_id}")
async def start_conversation(
    personality_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Start a new conversation for personality analysis"""
    try:
        # Verify personality belongs to user
        result = await db.execute(
            select(PersonalityProfile).where(
                PersonalityProfile.profile_id == personality_id,
                PersonalityProfile.user_id == current_user.user_id
            )
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # Create new conversation
        conversation = Conversation(
            user_id=current_user.user_id,
            personality_id=personality.profile_id
        )
        
        db.add(conversation)
        await db.commit()
        await db.refresh(conversation)
        
        # THINK phase: Generate initial questioning strategy
        strategy = await socratic_cycle.think_phase(personality_id, db)
        
        # ASK phase: Generate first question
        question = await socratic_cycle.ask_phase(strategy, [])
        
        # Save AI message
        ai_message = Message(
            conversation_id=conversation.conversation_id,
            sender="ai",
            content=question.question_text,
            analysis_data={"question_context": question.question_context}
        )
        
        db.add(ai_message)
        await db.commit()
        
        logger.info(
            "Conversation started",
            conversation_id=str(conversation.conversation_id),
            personality_id=personality_id
        )
        
        return {
            "conversation_id": str(conversation.conversation_id),
            "ai_response": question.question_text,
            "question_context": question.question_context
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to start conversation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start conversation"
        )

@app.post("/chat/respond")
async def respond_to_user(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Process user response and continue conversation"""
    try:
        if not request.conversation_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Conversation ID is required"
            )
        
        # Get conversation
        result = await db.execute(
            select(Conversation).where(
                Conversation.conversation_id == request.conversation_id,
                Conversation.user_id == current_user.user_id
            )
        )
        conversation = result.scalar_one_or_none()
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # Save user message
        user_message = Message(
            conversation_id=conversation.conversation_id,
            sender="user",
            content=request.user_input
        )
        
        db.add(user_message)
        await db.commit()
        
        # Get conversation history
        history_result = await db.execute(
            select(Message)
            .where(Message.conversation_id == conversation.conversation_id)
            .order_by(Message.timestamp)
        )
        messages = history_result.scalars().all()
        conversation_history = [f"{msg.sender}: {msg.content}" for msg in messages]
        
        # ANALYZE phase: Process user input
        analysis = await socratic_cycle.analyze_phase(
            user_input=request.user_input,
            personality_id=str(conversation.personality_id),
            conversation_history=conversation_history,
            db=db
        )
        
        # THINK phase: Plan next question
        strategy = await socratic_cycle.think_phase(str(conversation.personality_id), db)
        
        # ASK phase: Generate next question
        next_question = await socratic_cycle.ask_phase(strategy, conversation_history)
        
        # Save AI response
        ai_message = Message(
            conversation_id=conversation.conversation_id,
            sender="ai",
            content=next_question.question_text,
            analysis_data={
                "analysis_summary": analysis.psychological_summary,
                "confidence_score": analysis.confidence_score,
                "question_context": next_question.question_context
            }
        )
        
        db.add(ai_message)
        await db.commit()
        
        logger.info(
            "User response processed",
            conversation_id=request.conversation_id,
            confidence_score=analysis.confidence_score
        )
        
        return {
            "ai_response": next_question.question_text,
            "analysis_summary": analysis.psychological_summary,
            "confidence_score": analysis.confidence_score,
            "question_context": next_question.question_context
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to process user response", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process response"
        )

@app.post("/predict")
async def predict_response(
    request: PredictionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Predict how the cloned personality would respond to a situation"""
    try:
        # Verify personality belongs to user
        result = await db.execute(
            select(PersonalityProfile).where(
                PersonalityProfile.profile_id == request.personality_id,
                PersonalityProfile.user_id == current_user.user_id
            )
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # PREDICT phase: Generate prediction
        prediction = await socratic_cycle.predict_phase(
            personality_id=request.personality_id,
            situation=request.situation,
            db=db
        )
        
        logger.info(
            "Prediction generated",
            personality_id=request.personality_id,
            confidence=prediction.confidence
        )
        
        return {
            "predicted_response": prediction.predicted_response,
            "confidence": prediction.confidence,
            "reasoning": prediction.reasoning,
            "alternative_responses": prediction.alternative_responses
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Prediction failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate prediction"
        )

@app.post("/validate")
async def validate_personality(
    request: ValidationRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Validate personality cloning accuracy"""
    try:
        # Verify personality belongs to user
        result = await db.execute(
            select(PersonalityProfile).where(
                PersonalityProfile.profile_id == request.personality_id,
                PersonalityProfile.user_id == current_user.user_id
            )
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # VALIDATE phase: Assess similarity
        similarity = await socratic_cycle.validate_phase(
            personality_id=request.personality_id,
            reference_data=request.reference_data,
            db=db
        )
        
        logger.info(
            "Validation completed",
            personality_id=request.personality_id,
            overall_similarity=similarity.overall_similarity
        )
        
        return {
            "overall_similarity": similarity.overall_similarity,
            "dimension_similarities": similarity.dimension_similarities,
            "behavioral_similarity": similarity.behavioral_similarity,
            "communication_similarity": similarity.communication_similarity,
            "value_alignment": similarity.value_alignment,
            "strengths": similarity.strengths,
            "gaps": similarity.gaps,
            "improvement_suggestions": similarity.improvement_suggestions
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Validation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate personality"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
